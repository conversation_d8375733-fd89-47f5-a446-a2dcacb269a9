# FitWell Design Assets & Visual Guidelines

## Logo Design Specifications

### Primary Logo
**Concept:** Bold, modern wordmark with integrated fitness element
- Font: Custom Bebas Neue with modified letterforms
- Icon: Stylized dumbbell or abstract strength symbol
- Color: Primary red (#E63946) on white/black backgrounds
- Minimum size: 120px width for digital, 1 inch for print

### Logo Variations
1. **Horizontal Logo:** Full wordmark with tagline
2. **Stacked Logo:** Vertical arrangement for square spaces
3. **Icon Only:** Symbol mark for social media profiles
4. **Monochrome:** Black and white versions for single-color applications

### Logo Usage Guidelines
- Minimum clear space: 1/2 the height of the logo on all sides
- Never stretch, skew, or rotate the logo
- Maintain original proportions at all times
- Use approved color variations only

## Color System

### Primary Palette
```css
:root {
  --primary-red: #E63946;
  --secondary-black: #0D0D0D;
  --accent-white: #FFFFFF;
  --metallic-silver: #C0C0C0;
}
```

### Extended Palette
```css
:root {
  /* Red Variations */
  --red-light: #FF6B7A;
  --red-dark: #C62D36;
  
  /* Gray Scale */
  --gray-100: #F8F9FA;
  --gray-200: #E9ECEF;
  --gray-300: #DEE2E6;
  --gray-400: #CED4DA;
  --gray-500: #ADB5BD;
  --gray-600: #6C757D;
  --gray-700: #495057;
  --gray-800: #343A40;
  --gray-900: #212529;
  
  /* Functional Colors */
  --success: #28A745;
  --warning: #FFC107;
  --error: #DC3545;
  --info: #17A2B8;
}
```

### Color Usage Rules
- **Primary Red:** CTAs, highlights, active states, brand elements
- **Deep Black:** Text, headers, navigation, footer
- **White:** Background, text on dark surfaces, clean space
- **Silver:** Borders, dividers, subtle accents, equipment imagery

## Typography System

### Font Hierarchy
```css
/* Primary Font - Montserrat */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&display=swap');

/* Secondary Font - Poppins */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');

/* Display Font - Bebas Neue */
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');
```

### Typography Scale
```css
:root {
  /* Headings */
  --h1-size: 3.5rem;    /* 56px - Hero headlines */
  --h2-size: 2.5rem;    /* 40px - Section headers */
  --h3-size: 2rem;      /* 32px - Subsection headers */
  --h4-size: 1.5rem;    /* 24px - Card titles */
  --h5-size: 1.25rem;   /* 20px - Small headers */
  --h6-size: 1rem;      /* 16px - Captions */
  
  /* Body Text */
  --body-large: 1.125rem;  /* 18px - Important content */
  --body-regular: 1rem;    /* 16px - Standard text */
  --body-small: 0.875rem;  /* 14px - Secondary text */
  --caption: 0.75rem;      /* 12px - Fine print */
}
```

### Font Usage Guidelines
- **Bebas Neue:** Hero headlines, major CTAs, section dividers
- **Montserrat Bold:** Page headers, navigation, important buttons
- **Montserrat Semi-Bold:** Subheadings, card titles, form labels
- **Poppins Regular:** Body text, descriptions, form inputs
- **Poppins Light:** Supporting text, captions, fine print

## Button & CTA Design

### Primary Button Styles
```css
.btn-primary {
  background: var(--primary-red);
  color: var(--accent-white);
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  padding: 16px 32px;
  border-radius: 8px;
  border: none;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(230, 57, 70, 0.3);
}

.btn-primary:hover {
  background: var(--red-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(230, 57, 70, 0.4);
}
```

### Secondary Button Styles
```css
.btn-secondary {
  background: transparent;
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  padding: 14px 30px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-red);
  color: var(--accent-white);
  transform: translateY(-1px);
}
```

## Layout & Grid System

### Container Specifications
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.container-fluid {
  width: 100%;
  padding: 0 20px;
}

.container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}
```

### Grid System
```css
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
  }
}
```

## Component Design Specifications

### Navigation Header
**Desktop Navigation:**
- Fixed header with transparent background
- Logo on left, navigation center, CTA button right
- Smooth scroll behavior with background opacity change
- Dropdown menus for Programs and Resources

**Mobile Navigation:**
- Hamburger menu with slide-out panel
- Full-screen overlay with large touch targets
- Prominent membership CTA at top of menu

### Hero Section Design
**Layout:**
- Full viewport height (100vh)
- Video background with overlay
- Centered content with left-aligned text
- Dual CTA buttons with hierarchy

**Content Structure:**
```html
<section class="hero">
  <video class="hero-video" autoplay muted loop>
    <source src="hero-workout.mp4" type="video/mp4">
  </video>
  <div class="hero-overlay"></div>
  <div class="hero-content">
    <h1>Transform Your Body, Transform Your Life</h1>
    <p>Join FitWell and take the first step toward a healthier, stronger you</p>
    <div class="hero-ctas">
      <button class="btn-primary">Get Started</button>
      <button class="btn-secondary">Book Free Trial</button>
    </div>
  </div>
</section>
```

### Card Components
**Program Cards:**
- 16:9 aspect ratio images
- Overlay text with gradient background
- Hover effects with scale and shadow
- Clear pricing and CTA integration

**Testimonial Cards:**
- Member photo (circular crop)
- Star rating display
- Quote text with proper typography
- Member name and transformation details

### Form Design
**Contact Form Styling:**
```css
.form-group {
  margin-bottom: 1.5rem;
}

.form-input {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(230, 57, 70, 0.1);
}
```

## Image & Media Guidelines

### Photography Style
**Characteristics:**
- High contrast, dramatic lighting
- Action shots with motion blur
- Diverse representation of members
- Professional equipment focus
- Clean, modern facility shots

**Technical Specifications:**
- Minimum resolution: 1920x1080 for hero images
- Format: WebP with JPEG fallback
- Compression: 80% quality for web optimization
- Alt text: Descriptive and SEO-friendly

### Video Content
**Hero Video Requirements:**
- Duration: 15-30 seconds looping
- Resolution: 1920x1080 minimum
- Format: MP4 with H.264 encoding
- File size: Under 5MB for web performance
- Content: Dynamic workout scenes, equipment shots

### Icon System
**Style:** Outline icons with 2px stroke weight
**Library:** Feather Icons or custom SVG set
**Usage:** Navigation, features, social media, contact info
**Color:** Inherit from parent element or primary red

## Animation & Interaction Design

### Micro-Interactions
**Hover Effects:**
- Buttons: Scale (1.05x) + shadow increase
- Cards: Lift effect with shadow
- Images: Subtle zoom (1.1x) with overlay
- Links: Color transition + underline animation

**Loading Animations:**
- Skeleton screens for content loading
- Progress bars for form submissions
- Spinner animations for data fetching
- Smooth page transitions

### Scroll Animations
**Fade In Effects:**
- Stagger animations for card grids
- Slide up from bottom for text content
- Scale in for images and media
- Intersection Observer API implementation

## Responsive Design Breakpoints

### Mobile (320px - 767px)
- Single column layouts
- Stacked navigation menu
- Touch-optimized buttons (minimum 44px)
- Simplified hero content

### Tablet (768px - 1023px)
- Two-column layouts where appropriate
- Condensed navigation with dropdowns
- Optimized image sizes
- Adjusted typography scale

### Desktop (1024px+)
- Multi-column layouts
- Full navigation menu
- Larger hero sections
- Enhanced hover effects

## Brand Asset Checklist

### Required Assets
- [ ] Primary logo (SVG, PNG in multiple sizes)
- [ ] Logo variations (horizontal, stacked, icon-only)
- [ ] Color palette swatches
- [ ] Typography specimens
- [ ] Button style guide
- [ ] Icon library
- [ ] Photography style guide
- [ ] Video content guidelines

### File Organization
```
/assets
  /logos
    - fitwell-logo-primary.svg
    - fitwell-logo-horizontal.svg
    - fitwell-logo-stacked.svg
    - fitwell-icon.svg
  /images
    - hero-background.jpg
    - facility-photos/
    - trainer-headshots/
    - equipment-photos/
  /videos
    - hero-workout.mp4
    - facility-tour.mp4
  /icons
    - feather-icon-set.svg
```

This design asset guide ensures consistent visual implementation across all FitWell digital touchpoints while maintaining brand integrity and professional appearance.
